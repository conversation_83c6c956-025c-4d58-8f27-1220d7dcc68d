import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:wicker/services/places_service.dart';
import '../widgets/home_search_bar.dart';
import '../widgets/post_card.dart';
import 'detail_scroll_viewer.dart';

class HomeScreen extends StatefulWidget {
  final VoidCallback onSearchTap;
  const HomeScreen({super.key, required this.onSearchTap});

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final PlacesService _placesService = PlacesService();
  late Future<List<Map<String, dynamic>>> _placesFuture;

  // Define the base URL here to construct full image paths
  final String _baseUrl = defaultTargetPlatform == TargetPlatform.android
      ? "http://10.0.2.2:5000"
      : "http://127.0.0.1:5000";

  @override
  void initState() {
    super.initState();
    _placesFuture = _placesService.getPlaces();
  }

  // Helper to process place data fetched from the backend
  // In lib/screens/home_screen.dart -> _HomeScreenState

  Map<String, List<Map<String, dynamic>>> _processFetchedPlaces(
    List<Map<String, dynamic>> places,
  ) {
    Map<String, List<Map<String, dynamic>>> grouped = {};
    for (var place in places) {
      String category = place['category'] ?? 'Other';

      if (place['photos'] != null && (place['photos'] as List).isNotEmpty) {
        String imagePath = place['photos'][0];
        String correctedPath = imagePath.replaceAll('\\', '/');
        place['imageUrl'] = '$_baseUrl/$correctedPath';
      } else {
        place['imageUrl'] =
            'https://via.placeholder.com/400x300.png?text=No+Image';
      }

      // THE FIX: Cast 'created_by' to a Map before accessing the '$oid' key.

      //read Operator ID key from mongodb document
      final createdByMap = place['created_by'] as Map<String, dynamic>;

      final userId = createdByMap['\$oid'];
      place['avatarUrl'] = 'https://picsum.photos/seed/$userId/100';

      place['posterName'] = 'A User';
      place['views'] = (place['reviews'] as List).length.toString();
      place['postedTime'] = 'Just now';

      if (grouped.containsKey(category)) {
        grouped[category]!.add(place);
      } else {
        grouped[category] = [place];
      }
    }
    return grouped;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: FutureBuilder<List<Map<String, dynamic>>>(
        future: _placesFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          }
          if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text('No places found. Add one!'));
          }

          final allData = _processFetchedPlaces(snapshot.data!);

          // Flatten all places into a single list for the main feed
          final allPosts = allData.values.expand((posts) => posts).toList();

          return Column(
            children: [
              Container(
                padding: const EdgeInsets.only(top: 40.0, bottom: 8.0),
                child: HomeSearchBar(onTap: widget.onSearchTap),
              ),
              Expanded(
                child: ListView.builder(
                  padding: EdgeInsets.zero,
                  itemCount: allPosts.length,
                  itemBuilder: (context, index) {
                    final post = allPosts[index];
                    return PostCard(
                      postData: post.map((k, v) => MapEntry(k, v.toString())),
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
