import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:image_picker/image_picker.dart';
import 'auth_service.dart';

class PostService {
  final String _baseUrl = defaultTargetPlatform == TargetPlatform.android
      ? "http://********:5000"
      : "http://127.0.0.1:5000";

  final AuthService _authService = AuthService();

  Future<String> createPost({
    required String textContent,
    String? taggedPlaceId,
    List<XFile>? mediaFiles,
  }) async {
    final token = await _authService.getAccessToken();
    if (token == null) throw Exception('Authentication Token not found.');

    var request = http.MultipartRequest(
      'POST',
      Uri.parse('$_baseUrl/api/posts/create'),
    );
    request.headers['Authorization'] = 'Bearer $token';

    // Add text fields
    request.fields['text_content'] = textContent;
    if (taggedPlaceId != null) {
      request.fields['tagged_place_id'] = taggedPlaceId;
    }

    // Add media files
    if (mediaFiles != null && mediaFiles.isNotEmpty) {
      for (var mediaFile in mediaFiles) {
        request.files.add(
          await http.MultipartFile.fromPath(
            'media', // This key must match the backend
            mediaFile.path,
            contentType: MediaType(
              'application',
              'octet-stream',
            ), // Generic content type
          ),
        );
      }
    }

    try {
      var streamedResponse = await request.send();
      var response = await http.Response.fromStream(streamedResponse);
      final responseBody = jsonDecode(response.body);

      if (response.statusCode == 201) {
        return responseBody['msg'] ?? 'Post created successfully';
      } else {
        throw Exception(responseBody['msg'] ?? 'Failed to create post');
      }
    } catch (e) {
      print('Create post error: $e');
      rethrow;
    }
  }
}
