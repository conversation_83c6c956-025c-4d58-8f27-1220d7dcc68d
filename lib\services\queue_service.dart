import 'dart:convert';
import 'package:wicker/services/places_service.dart'; // We'll use the WickerHttpClient

class QueueService {
  final WickerHttpClient _client = WickerHttpClient();
  final String _baseUrl = "http://127.0.0.1:5000";

  Future<String> createQueue({
    required String name,
    required bool isPrivate,
  }) async {
    try {
      final response = await _client.post(
        Uri.parse('$_baseUrl/api/queues/create'),
        body: jsonEncode({'name': name, 'is_private': isPrivate}),
      );

      final responseBody = jsonDecode(response.body);
      if (response.statusCode == 201) {
        return responseBody['msg'];
      } else {
        throw Exception(responseBody['msg'] ?? 'Failed to create queue');
      }
    } catch (e) {
      rethrow;
    }
  }
}
