import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:wicker/services/places_service.dart'; // We'll use the WickerHttpClient

class PlaylistService {
  final WickerHttpClient _client = WickerHttpClient();
  // Base URL can be defined here or inherited
  final String _baseUrl = "http://127.0.0.1:5000";

  Future<String> createPlaylist({
    required String name,
    required bool isPrivate,
  }) async {
    try {
      final response = await _client.post(
        Uri.parse('$_baseUrl/api/playlists/create'),
        body: jsonEncode({'name': name, 'is_private': isPrivate}),
      );

      final responseBody = jsonDecode(response.body);
      if (response.statusCode == 201) {
        return responseBody['msg'];
      } else {
        throw Exception(responseBody['msg'] ?? 'Failed to create playlist');
      }
    } catch (e) {
      rethrow;
    }
  }
}
